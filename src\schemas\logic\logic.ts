import * as z from "zod";
import { FormElement } from "@/components/types";

const createConditionSchema = (formElements?: FormElement[]) =>
  z
    .object({
      id: z.string(),
      componentId: z.string().min(1, { message: "Enter parent question" }),
      operator: z.string().min(1, { message: "Enter operator" }),
      value: z.union([z.string(), z.number()]).optional(),
      minimumValue: z.union([z.string(), z.number()]).optional(),
      maximumValue: z.union([z.string(), z.number()]).optional(),
      logic: z.string().nullable(),
    })
    .superRefine((data, ctx) => {
      const addIssue = (field: string, message: string) => {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message,
          path: [field],
        });
      };

      const isEmpty = (value: any) => !value || value === "";
      const isRangeOperator = data.operator === "is in range";

      // Get number field constraints if available
      const getNumberFieldConstraints = () => {
        if (!formElements || !data.componentId) return { min: undefined, max: undefined };

        const targetElement = formElements.find(element => element.id === data.componentId);

        if (targetElement && targetElement.type === "NumberField") {
          return {
            min: targetElement.minimumValue,
            max: targetElement.maximumValue,
          };
        }

        return { min: undefined, max: undefined };
      };

      const { min: numberMin, max: numberMax } = getNumberFieldConstraints();

      // Validate range operator fields
      if (isRangeOperator) {
        if (isEmpty(data.minimumValue)) addIssue("minimumValue", "Enter minimum value");
        if (isEmpty(data.maximumValue)) addIssue("maximumValue", "Enter maximum value");

        // Validate number field constraints for range values
        if (numberMin !== undefined || numberMax !== undefined) {
          const minVal = Number(data.minimumValue);
          const maxVal = Number(data.maximumValue);

          if (!isNaN(minVal) && numberMin !== undefined && minVal < numberMin) {
            addIssue("minimumValue", `Minimum value cannot be less than ${numberMin}`);
          }
          if (!isNaN(minVal) && numberMax !== undefined && minVal > numberMax) {
            addIssue("minimumValue", `Minimum value cannot be greater than ${numberMax}`);
          }
          if (!isNaN(maxVal) && numberMin !== undefined && maxVal < numberMin) {
            addIssue("maximumValue", `Maximum value cannot be less than ${numberMin}`);
          }
          if (!isNaN(maxVal) && numberMax !== undefined && maxVal > numberMax) {
            addIssue("maximumValue", `Maximum value cannot be greater than ${numberMax}`);
          }
        }
        return;
      }

      // Validate single value field
      if (isEmpty(data.value)) {
        addIssue("value", "Enter value");
        return;
      }

      // Validate number field constraints for single value
      if (numberMin !== undefined || numberMax !== undefined) {
        const numValue = Number(data.value);

        if (!isNaN(numValue)) {
          if (numberMin !== undefined && numValue < numberMin) {
            addIssue("value", `Value cannot be less than ${numberMin}`);
          }
          if (numberMax !== undefined && numValue > numberMax) {
            addIssue("value", `Value cannot be greater than ${numberMax}`);
          }
        }
      }
    });

// Default schema for backward compatibility
const conditionSchema = createConditionSchema();

const createLogicSchema = (formElements?: FormElement[]) =>
  z.object({
    id: z.string(),
    conditions: z.array(createConditionSchema(formElements)).min(1, "At least one condition is required"),
    action: z.string().min(1, "Action is required"),
  });

export const createElementLogicSchema = (formElements?: FormElement[]) =>
  z.object({
    logics: z.array(createLogicSchema(formElements)).min(1, "At least one logic is required"),
  });

// Default schemas for backward compatibility
const logicSchema = createLogicSchema();
export const elementLogicSchema = createElementLogicSchema();
