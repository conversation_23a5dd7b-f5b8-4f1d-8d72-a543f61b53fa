import { NUMBER_LIMIT, PARAGRAPH_CHARACTER_COUNT, PHONE_NUMBER_LIMIT, QR_CODE_RULE_TYPE, QR_CODE_VALIDATION_TYPE } from "@/lib/constants";
import { z } from "zod";

const ruleSchema = z
  .object({
    id: z.string(),
    type: z.string(),
    minimumCharacterCount: z
      .number({ message: "Enter minimum character count" })
      .min(PARAGRAPH_CHARACTER_COUNT.MINIMUM, { message: `Minimum character count is ${PARAGRAPH_CHARACTER_COUNT.MINIMUM}` })
      .optional(),
    maximumCharacterCount: z
      .number({ message: "Enter maximum character count" })
      .max(PARAGRAPH_CHARACTER_COUNT.MAXIMUM, { message: `Maximum character count is ${PARAGRAPH_CHARACTER_COUNT.MAXIMUM}` })
      .optional(),
    exactCharacterCount: z
      .number({ message: "Enter exact character count" })
      .min(PARAGRAPH_CHARACTER_COUNT.MINIMUM, {
        message: `Exact character count must be at least ${PARAGRAPH_CHARACTER_COUNT.MINIMUM}`,
      })
      .max(PARAGRAPH_CHARACTER_COUNT.MAXIMUM, {
        message: `Exact character count cannot exceed ${PARAGRAPH_CHARACTER_COUNT.MAXIMUM}`,
      })
      .optional(),
    patternType: z.string({ message: "Enter pattern type" }).optional(),
    patternText: z.string({ message: "Enter pattern text" }).optional(),
  })
  .superRefine((data, ctx) => {
    if (data.type === QR_CODE_RULE_TYPE.pattern && !data.patternType) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Pattern type is required",
        path: ["patternType"],
      });
    }

    if (data.type === QR_CODE_RULE_TYPE.pattern && !data.patternText) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Pattern text is required",
        path: ["patternText"],
      });
    }

    if (data.type === QR_CODE_RULE_TYPE.length) {
      if (data.exactCharacterCount) {
        return;
      }

      if (!data.minimumCharacterCount) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Either exact character count or both minimum and maximum character counts are required",
          path: ["minimumCharacterCount"],
        });
      }

      if (!data.maximumCharacterCount) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Either exact character count or both minimum and maximum character counts are required",
          path: ["maximumCharacterCount"],
        });
      }

      if (data.minimumCharacterCount && data.maximumCharacterCount && data.minimumCharacterCount >= data.maximumCharacterCount) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Minimum character count must be less than maximum character count",
          path: ["minimumCharacterCount"],
        });
      }
    }
  });

export const qrCodeScanPropertiesSchema = z
  .object({
    validationType: z.string(),
    allowDecimals: z.boolean().optional(),
    currency: z.boolean().optional(),
    includeTimeValidation: z.boolean().optional(),
    includeCountryCode: z.boolean().optional(),
    minimumValue: z
      .number({ message: "Enter minimum value" })
      .min(NUMBER_LIMIT.MINIMUM, { message: `Minimum value is ${NUMBER_LIMIT.MINIMUM}` })
      .optional(),
    maximumValue: z
      .number({ message: "Enter maximum value" })
      .max(NUMBER_LIMIT.MAXIMUM, { message: `Maximum value is ${NUMBER_LIMIT.MAXIMUM}` })
      .optional(),
    minimumLength: z
      .number({ message: "Enter minimum length" })
      .min(PHONE_NUMBER_LIMIT.MINIMUM, {
        message: `Minimum length is ${PHONE_NUMBER_LIMIT.MINIMUM}`,
      })
      .optional(),
    maximumLength: z
      .number({ message: "Enter maximum length" })
      .max(PHONE_NUMBER_LIMIT.MAXIMUM, {
        message: `Maximum length is ${PHONE_NUMBER_LIMIT.MAXIMUM}`,
      })
      .optional(),
    minimumDateRange: z.string({ message: "Enter minimum date" }).optional(),
    maximumDateRange: z.string({ message: "Enter maximum date" }).optional(),
    rules: z.array(ruleSchema).optional(),
  })
  .superRefine((data, ctx) => {
    // Validate unique rule types and pattern types
    if (data.rules) {
      const lengthRules = data.rules.filter(rule => rule.type === QR_CODE_RULE_TYPE.length);
      if (lengthRules.length > 1) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Only one length rule is allowed",
          path: ["rules"],
        });
      }

      // const patternRules = data.rules.filter(rule => rule.type === QR_CODE_RULE_TYPE.pattern);
      // const usedPatternTypes = new Set();

      // patternRules.forEach((rule, index) => {
      //   if (rule.patternType && usedPatternTypes.has(rule.patternType)) {
      //     ctx.addIssue({
      //       code: z.ZodIssueCode.custom,
      //       message: "Each pattern type can only be used once",
      //       path: ["rules", index, "patternType"],
      //     });
      //   }
      //   if (rule.patternType) {
      //     usedPatternTypes.add(rule.patternType);
      //   }
      // });
    }

    if (data.validationType === QR_CODE_VALIDATION_TYPE.text) {
      if (!data.rules || data.rules.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Rules are required for text validation",
          path: ["rules"],
        });
      }
    }

    if (data.validationType === QR_CODE_VALIDATION_TYPE.number) {
      if (!data.minimumValue) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Minimum value is required",
          path: ["minimumValue"],
        });
      }

      if (!data.maximumValue) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Maximum value is required",
          path: ["maximumValue"],
        });
      }

      if (data.minimumValue && data.maximumValue && data.minimumValue >= data.maximumValue) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Minimum value must be less than maximum value",
          path: ["minimumValue"],
        });
      }
    }

    if (data.validationType === QR_CODE_VALIDATION_TYPE.phoneNumber) {
      if (!data.minimumLength) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Minimum length is required",
          path: ["minimumLength"],
        });
      }

      if (!data.maximumLength) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Maximum length is required",
          path: ["maximumLength"],
        });
      }

      if (data.minimumLength && data.maximumLength && data.minimumLength >= data.maximumLength) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Minimum length must be less than maximum length",
          path: ["minimumLength"],
        });
      }
    }

    if (data.validationType === QR_CODE_VALIDATION_TYPE.date) {
      if (!data.minimumDateRange) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Minimum range is required",
          path: ["minimumDateRange"],
        });
      }

      if (!data.maximumDateRange) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Maximum range is required",
          path: ["maximumDateRange"],
        });
      }

      if (data.minimumDateRange && data.maximumDateRange && new Date(data.minimumDateRange) >= new Date(data.maximumDateRange)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Minimum date must be before maximum date",
          path: ["minimumDateRange"],
        });
      }
    }
  });
