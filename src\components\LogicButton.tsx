import { fredoka } from "@/app/fonts";
import plusIcon from "@/assets/icons/plus-green.svg";
import eyeIcon from "@/assets/icons/square-eye.svg";
import { LogicButtonProps } from "@/components/types";
import { Button } from "@/components/ui/button";
import { useAppDispatch } from "@/hooks/use-redux";
import { openLogicModal } from "@/lib/redux/slices/dialogSlice";
import Image from "next/image";
import { useState } from "react";
import { VscTriangleRight } from "react-icons/vsc";
import LogicModal from "./dialogs/LogicModal";

const LogicButton = ({ isLogicPresent = false, screenId, sectionId }: LogicButtonProps) => {
  const dispatch = useAppDispatch();
  const [isLogicOpen, setIsLogicOpen] = useState<boolean>(true);

  const handleLogicModalOpen = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    dispatch(openLogicModal());
  };

  return (
    <>
      <Button
        variant="ghost"
        className={`${fredoka.className} cursor-pointer items-center gap-1 p-0 text-lg font-semibold hover:bg-transparent`}
        onClick={() => setIsLogicOpen(!isLogicOpen)}
      >
        <VscTriangleRight className={`${isLogicOpen ? "rotate-90" : "rotate-0"} transition duration-200 ease-in-out`} /> Logic
      </Button>
      {isLogicOpen && (
        <Button
          variant="ghost"
          className="flex cursor-pointer items-center gap-2 p-0 font-normal hover:bg-transparent"
          onClick={handleLogicModalOpen}
        >
          <Image src={isLogicPresent ? eyeIcon : plusIcon} alt={isLogicPresent ? "Eye Icon" : "Plus Icon"} width={14} height={14} />
          {isLogicPresent ? "View Logic" : "Add Logic"}
        </Button>
      )}
      <LogicModal screenId={screenId} sectionId={sectionId} />
    </>
  );
};

export default LogicButton;
