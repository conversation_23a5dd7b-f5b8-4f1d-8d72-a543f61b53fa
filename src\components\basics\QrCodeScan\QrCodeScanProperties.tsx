import { QrCodeScanPropertiesFormData } from "@/app/(core)/forms/types";
import deleteIcon from "@/assets/icons/delete-icon.svg";
import plusIcon from "@/assets/icons/plus-green.svg";
import DatePicker from "@/components/DatePicker";
import NumberInput from "@/components/NumberInput";
import { FormElement } from "@/components/types";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useGetSingleForm } from "@/hooks/tansack-query/queries/use-forms";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { QR_CODE_PATTERN_TYPE, QR_CODE_RULE_TYPE, QR_CODE_VALIDATION_TYPE } from "@/lib/constants";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId, generateId } from "@/lib/utils";
import { qrCodeScanPropertiesSchema } from "@/schemas/properties/qrCodeScanProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import * as changeCase from "change-case";
import Image from "next/image";
import { useEffect, useMemo } from "react";
import { useFieldArray, useForm } from "react-hook-form";

const QrCodeScanProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const formId = useAppSelector(state => state.form.formId);
  const elementId = selectedFormBuilderItem?.id ?? "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) ?? "";
  }

  const { singleFormData } = useGetSingleForm(formId);
  const validationTypes = ["Allow all types", "Text", "Number", "Email", "URL", "Phone Number", "Date"];
  const ruleTypes = ["Length", "Pattern"];
  const patternTypes = ["Contains", "Equal to", "Starts with", "Ends with"];

  const form = useForm<QrCodeScanPropertiesFormData>({
    resolver: zodResolver(qrCodeScanPropertiesSchema),
    mode: "all",
    reValidateMode: "onChange",
    defaultValues: {
      validationType: selectedFormBuilderItem?.validationType,
      rules: selectedFormBuilderItem?.rules || [],
      minimumLength: selectedFormBuilderItem?.minimumLength,
      maximumLength: selectedFormBuilderItem?.maximumLength,
      includeCountryCode: selectedFormBuilderItem?.includeCountryCode || false,
      minimumValue: selectedFormBuilderItem?.minimumValue,
      maximumValue: selectedFormBuilderItem?.maximumValue,
      allowDecimals: selectedFormBuilderItem?.allowDecimals || false,
      currency: selectedFormBuilderItem?.currency || false,
      includeTimeValidation: selectedFormBuilderItem?.includeTimeValidation || false,
      minimumDateRange: selectedFormBuilderItem?.minimumDateRange || "",
      maximumDateRange: selectedFormBuilderItem?.maximumDateRange || "",
    },
    shouldFocusError: false,
  });

  const {
    setValue,
    watch,
    control,
    formState: { errors },
  } = form;

  const {
    fields: rulesFields,
    append: appendRule,
    remove: removeRule,
  } = useFieldArray({
    control,
    name: "rules",
  });

  // Helper functions to track used rule types and pattern types
  const usedRuleTypes = useMemo(() => {
    const rules = watch("rules") || [];
    return rules.map(rule => rule.type).filter(Boolean);
  }, [watch("rules")]);

  // Check if we have both length and pattern rules (max 2 rules total)
  const hasLengthRule = useMemo(() => {
    return usedRuleTypes.includes(QR_CODE_RULE_TYPE.length);
  }, [usedRuleTypes]);

  const hasPatternRule = useMemo(() => {
    return usedRuleTypes.includes(QR_CODE_RULE_TYPE.pattern);
  }, [usedRuleTypes]);

  // Disable Add Rule button when we already have both types
  const canAddRule = useMemo(() => {
    return !(hasLengthRule && hasPatternRule);
  }, [hasLengthRule, hasPatternRule]);

  const isRuleTypeDisabled = (ruleType: string, currentIndex: number) => {
    if (ruleType === QR_CODE_RULE_TYPE.length) {
      // Check if length is already used in any other rule
      return usedRuleTypes.some((type, index) => type === QR_CODE_RULE_TYPE.length && index !== currentIndex);
    }
    if (ruleType === QR_CODE_RULE_TYPE.pattern) {
      // Check if pattern is already used in any other rule
      return usedRuleTypes.some((type, index) => type === QR_CODE_RULE_TYPE.pattern && index !== currentIndex);
    }
    return false;
  };

  const isPatternTypeDisabled = (patternType: string | undefined, currentIndex: number) => {
    if (!patternType) return false;
    // Check if this pattern type is already used in any other pattern rule
    const rules = watch("rules") || [];
    return rules.some((rule, index) => rule.type === QR_CODE_RULE_TYPE.pattern && rule.patternType === patternType && index !== currentIndex);
  };

  const handleAddRule = () => {
    // Don't allow adding if we already have both rule types
    if (!canAddRule) return;

    form.trigger("rules");

    // Determine the default rule type based on what's already used
    const defaultRuleType = hasLengthRule ? QR_CODE_RULE_TYPE.pattern : QR_CODE_RULE_TYPE.length;

    appendRule({
      id: `rule_${generateId()}`,
      type: defaultRuleType,
    });

    // If adding a pattern rule, trigger validation after a short delay
    if (defaultRuleType === QR_CODE_RULE_TYPE.pattern) {
      setTimeout(() => {
        const newRuleIndex = (form.getValues("rules") || []).length - 1;
        form.trigger(`rules.${newRuleIndex}.patternType`);
        form.trigger(`rules.${newRuleIndex}.patternText`);
      }, 100);
    }
  };

  useEffect(() => {
    setValue("validationType", selectedFormBuilderItem.validationType as string);
    setValue("rules", selectedFormBuilderItem.rules || []);
    setValue("minimumLength", selectedFormBuilderItem.minimumLength);
    setValue("maximumLength", selectedFormBuilderItem.maximumLength);
    setValue("includeCountryCode", selectedFormBuilderItem.includeCountryCode);
    setValue("minimumValue", selectedFormBuilderItem.minimumValue);
    setValue("maximumValue", selectedFormBuilderItem.maximumValue);
    setValue("allowDecimals", selectedFormBuilderItem.allowDecimals);
    setValue("currency", selectedFormBuilderItem.currency);
    setValue("includeTimeValidation", selectedFormBuilderItem.includeTimeValidation);
    setValue("minimumDateRange", selectedFormBuilderItem.minimumDateRange);
    setValue("maximumDateRange", selectedFormBuilderItem.maximumDateRange);

    // Reset the form fields to match the new selected element
    // form.reset({
    //   validationType: selectedFormBuilderItem.validationType as string,
    //   rules: selectedFormBuilderItem.rules || [],
    // });
  }, [selectedFormBuilderItem]);

  // Effect to trigger validation for pattern rules when they become visible
  useEffect(() => {
    const currentRules = watch("rules") || [];
    currentRules.forEach((rule: any, index: number) => {
      if (rule?.type === QR_CODE_RULE_TYPE.pattern && !rule?.patternType) {
        form.trigger(`rules.${index}.patternType`);
      }
    });
  }, [watch("rules"), form]);

  const updateFormElements = (data: QrCodeScanPropertiesFormData) => {
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...data,
    } as FormElement;

    if (data.validationType !== QR_CODE_VALIDATION_TYPE.number) {
      delete newFormElement["minimumValue"];
      delete newFormElement["maximumValue"];
      delete newFormElement["allowDecimals"];
      delete newFormElement["currency"];
    }

    if (data.validationType !== QR_CODE_VALIDATION_TYPE.phoneNumber) {
      delete newFormElement["minimumLength"];
      delete newFormElement["maximumLength"];
      delete newFormElement["includeCountryCode"];
    }

    if (data.validationType !== QR_CODE_VALIDATION_TYPE.date) {
      delete newFormElement["includeTimeValidation"];
      delete newFormElement["minimumDateRange"];
      delete newFormElement["maximumDateRange"];
    }

    if (data.validationType !== QR_CODE_VALIDATION_TYPE.text) {
      delete newFormElement["rules"];
    }

    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(updateFormElements)}>
        <div className="flex flex-col gap-2">
          <FormField
            control={form.control}
            name="validationType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Validation Type</FormLabel>
                <Select value={field.value} onValueChange={field.onChange}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {validationTypes.map(type => (
                      <SelectItem value={changeCase.snakeCase(type)} key={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          {watch("validationType") === QR_CODE_VALIDATION_TYPE.number && (
            <>
              <p>Values</p>
              <div className="flex items-center gap-2">
                <FormField
                  control={form.control}
                  name="minimumValue"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <NumberInput
                          {...field}
                          value={field.value}
                          placeholder="Min"
                          onChange={field.onChange}
                          className={`${errors.minimumValue && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="maximumValue"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <NumberInput
                          {...field}
                          value={field.value}
                          placeholder="Max"
                          onChange={field.onChange}
                          className={`${errors.maximumValue && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="allowDecimals"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-1">
                      <FormControl>
                        <Checkbox checked={!!field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Allow decimal numbers</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="currency"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-1">
                      <FormControl>
                        <Checkbox checked={!!field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Currency</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </>
          )}
          {watch("validationType") === QR_CODE_VALIDATION_TYPE.phoneNumber && (
            <>
              <p>Length</p>
              <div className="flex items-center gap-2">
                <FormField
                  control={form.control}
                  name="minimumLength"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <NumberInput
                          {...field}
                          value={field.value}
                          placeholder="Min Digits"
                          onChange={field.onChange}
                          className={`${errors.minimumLength && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="maximumLength"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <NumberInput
                          {...field}
                          value={field.value}
                          placeholder="Max Digits"
                          onChange={field.onChange}
                          className={`${errors.maximumLength && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="includeCountryCode"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-1">
                      <FormControl>
                        <Checkbox checked={!!field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Include Country Code</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
              {watch("includeCountryCode") && (
                <div className="h-[3rem] rounded-[10px] border border-primary-gray bg-primary-gray/10 p-3">{singleFormData?.country?.phone_code}</div>
              )}
            </>
          )}
          {watch("validationType") === QR_CODE_VALIDATION_TYPE.date && (
            <>
              <FormField
                control={form.control}
                name="includeTimeValidation"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-1">
                      <FormControl>
                        <Checkbox checked={!!field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Include time validation</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="minimumDateRange"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Date</FormLabel>
                    <FormControl>
                      <DatePicker
                        value={field.value ? new Date(field.value) : undefined}
                        onChange={date => {
                          if (date) {
                            form.setValue("minimumDateRange", date.toISOString(), { shouldValidate: true });
                            // Trigger form update after setting the value
                            form.handleSubmit(updateFormElements)();
                          } else {
                            form.setValue("minimumDateRange", undefined, { shouldValidate: true });
                            // Trigger form update after setting the value
                            form.handleSubmit(updateFormElements)();
                          }
                        }}
                        className={`w-full ${errors.minimumDateRange && "border-destructive"}`}
                        placeholder="dd/mm/yyyy"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="maximumDateRange"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Maximum Date</FormLabel>
                    <FormControl>
                      <DatePicker
                        value={field.value ? new Date(field.value) : undefined}
                        onChange={date => {
                          if (date) {
                            form.setValue("maximumDateRange", date.toISOString(), { shouldValidate: true });
                            // Trigger form update after setting the value
                            form.handleSubmit(updateFormElements)();
                          } else {
                            form.setValue("maximumDateRange", undefined, { shouldValidate: true });
                            // Trigger form update after setting the value
                            form.handleSubmit(updateFormElements)();
                          }
                        }}
                        className={`w-full ${errors.maximumDateRange && "border-destructive"}`}
                        placeholder="dd/mm/yyyy"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
          {watch("validationType") === QR_CODE_VALIDATION_TYPE.text && (
            <div className="">
              <Button
                type="button"
                variant="ghost"
                className={`items-center gap-2 p-0 font-normal hover:bg-transparent disabled:border-none ${
                  canAddRule ? "cursor-pointer" : "cursor-not-allowed opacity-50"
                }`}
                onClick={handleAddRule}
                disabled={!canAddRule}
              >
                <Image src={plusIcon} alt="Plus Icon" width={14} height={14} />
                Add Rule
              </Button>
              {rulesFields.map((rule, ruleIndex) => (
                <div key={rule.id}>
                  <p>Rule {ruleIndex + 1}</p>
                  <div className="mt-2 space-y-4">
                    <FormField
                      control={form.control}
                      name={`rules.${ruleIndex}.type`}
                      render={({ field }) => (
                        <FormItem>
                          <Select
                            value={field.value}
                            onValueChange={value => {
                              field.onChange(value);
                              if (value === QR_CODE_RULE_TYPE.length) {
                                // Clear pattern-related fields when switching to length
                                form.setValue(`rules.${ruleIndex}.patternType`, undefined);
                                form.setValue(`rules.${ruleIndex}.patternText`, undefined);
                                // Clear character count fields to start fresh
                                form.setValue(`rules.${ruleIndex}.minimumCharacterCount`, undefined);
                                form.setValue(`rules.${ruleIndex}.maximumCharacterCount`, undefined);
                                form.setValue(`rules.${ruleIndex}.exactCharacterCount`, undefined);
                              }

                              if (value === QR_CODE_RULE_TYPE.pattern) {
                                // Clear pattern type to force user selection
                                form.setValue(`rules.${ruleIndex}.patternType`, undefined);
                                // Clear character count fields when switching to pattern
                                form.setValue(`rules.${ruleIndex}.minimumCharacterCount`, undefined);
                                form.setValue(`rules.${ruleIndex}.maximumCharacterCount`, undefined);
                                form.setValue(`rules.${ruleIndex}.exactCharacterCount`, undefined);
                                // Clear pattern text to start fresh
                                form.setValue(`rules.${ruleIndex}.patternText`, undefined);
                                // Trigger validation for pattern type and pattern text
                                setTimeout(() => {
                                  form.trigger(`rules.${ruleIndex}.patternType`);
                                  form.trigger(`rules.${ruleIndex}.patternText`);
                                  form.trigger("rules");
                                }, 100);
                              }
                            }}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {ruleTypes.map(type => {
                                const ruleTypeValue = changeCase.snakeCase(type);
                                const disabled = isRuleTypeDisabled(ruleTypeValue, ruleIndex);
                                return (
                                  <SelectItem value={ruleTypeValue} key={type} disabled={disabled}>
                                    {type}
                                  </SelectItem>
                                );
                              })}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />

                    {watch(`rules.${ruleIndex}.type`) === QR_CODE_RULE_TYPE.length && (
                      <>
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name={`rules.${ruleIndex}.minimumCharacterCount`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <NumberInput
                                    {...field}
                                    value={field.value}
                                    placeholder="Min"
                                    onChange={field.onChange}
                                    className={`${errors.rules?.[ruleIndex]?.minimumCharacterCount && "border-destructive"}`}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name={`rules.${ruleIndex}.maximumCharacterCount`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <NumberInput
                                    {...field}
                                    value={field.value}
                                    placeholder="Max"
                                    onChange={field.onChange}
                                    className={`${errors.rules?.[ruleIndex]?.maximumCharacterCount && "border-destructive"}`}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        <FormField
                          control={form.control}
                          name={`rules.${ruleIndex}.exactCharacterCount`}
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <NumberInput
                                  {...field}
                                  value={field.value}
                                  placeholder="Exact Character Count"
                                  onChange={field.onChange}
                                  className={`${errors.rules?.[ruleIndex]?.exactCharacterCount && "border-destructive"}`}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <span className="text-xs">This will override min/max if specified</span>
                      </>
                    )}

                    {watch(`rules.${ruleIndex}.type`) === QR_CODE_RULE_TYPE.pattern && (
                      <div className="space-y-4">
                        <FormField
                          control={form.control}
                          name={`rules.${ruleIndex}.patternType`}
                          render={({ field }) => (
                            <FormItem>
                              <Select
                                value={field.value || ""}
                                onValueChange={value => {
                                  field.onChange(value);
                                  form.trigger(`rules.${ruleIndex}.patternType`);
                                }}
                                onOpenChange={open => {
                                  if (!open && !field.value) {
                                    // Trigger validation when dropdown closes without selection
                                    form.trigger(`rules.${ruleIndex}.patternType`);
                                  }
                                }}
                              >
                                <FormControl>
                                  <SelectTrigger
                                    className={`${errors.rules?.[ruleIndex]?.patternType && "border-destructive"}`}
                                    onBlur={() => {
                                      if (!field.value) {
                                        form.trigger(`rules.${ruleIndex}.patternType`);
                                      }
                                    }}
                                  >
                                    <SelectValue placeholder="Select pattern type" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {patternTypes.map(type => {
                                    const patternTypeValue = changeCase.snakeCase(type);
                                    const disabled = isPatternTypeDisabled(patternTypeValue, ruleIndex);
                                    return (
                                      <SelectItem value={patternTypeValue} key={type} disabled={disabled}>
                                        {type}
                                      </SelectItem>
                                    );
                                  })}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`rules.${ruleIndex}.patternText`}
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input
                                  {...field}
                                  value={field.value || ""}
                                  placeholder="Enter pattern text"
                                  className={`${errors.rules?.[ruleIndex]?.patternText && "border-destructive"}`}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    className="cursor-pointer items-center gap-2 p-0 font-normal text-destructive hover:bg-transparent hover:text-destructive"
                    onClick={() => removeRule(ruleIndex)}
                  >
                    <Image src={deleteIcon} alt="Delete Icon" width={14} height={14} />
                    Delete Rule
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      </form>
    </Form>
  );
};

export default QrCodeScanProperties;
